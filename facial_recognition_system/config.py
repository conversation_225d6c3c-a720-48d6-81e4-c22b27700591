from pathlib import Path

class Config:
    APP_NAME = "Teacher Assistant"
    VERSION = "1.0.0"

    USER_DATA_DIR = Path.home() / ".teacher_assistant"
    DATASET_DIR = USER_DATA_DIR / "dataset"
    DATA_DIR = DATASET_DIR / "data"
    TEST_DIR = DATASET_DIR / "test"
    UPLOADS_DIR = USER_DATA_DIR / "uploads"

    DB_FILE = "database.sqlite"
    DB_MODE = "local"

    TOLERANCE = 0.5
    IMAGE_SCALE_FACTOR = 0.7
    NUM_WORKERS = 4
    FACE_MODEL = "hog"  # Default model, will be overridden based on CUDA availability

    DEFAULT_PORT = 8550
    ENROLLMENT_PORT = 8551
    VIDEO_STREAM_PORT = 8552
    QUIZ_PORT = 8553
    HOST = "0.0.0.0"

    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800

    @classmethod
    def ensure_directories(cls):
        directories = [
            cls.USER_DATA_DIR,
            cls.DATASET_DIR,
            cls.DATA_DIR,
            cls.TEST_DIR,
            cls.UPLOADS_DIR
        ]
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    @classmethod
    def get_db_path(cls) -> str:
        cls.ensure_directories()
        return str(cls.DB_FILE)

Config.ensure_directories()

USER_DATA_DIR = str(Config.USER_DATA_DIR)
DATA_DIR = str(Config.DATA_DIR)
TEST_DIR = str(Config.TEST_DIR)
DB_FILE = str(Config.DB_FILE)
TOLERANCE = Config.TOLERANCE
IMAGE_SCALE_FACTOR = Config.IMAGE_SCALE_FACTOR
NUM_WORKERS = Config.NUM_WORKERS
DB_MODE = Config.DB_MODE
