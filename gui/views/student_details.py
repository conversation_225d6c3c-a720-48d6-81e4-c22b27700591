"""
Student details view for displaying comprehensive student information.
"""
import os
import flet as ft
from gui.components.app_bar import create_app_bar
from gui.components.navigation import create_navigation_rail, create_navigation_drawer
from gui.services.student_details_service import (
    get_student_details,
    get_quiz_submission_details,
    get_all_student_quiz_submissions
)
from gui.config.language import get_text
from gui.config.constants import ROUTE_CLASSES
from gui.components.dialogs import show_dialog, close_dialog
from datetime import datetime


def create_student_details_view(page: ft.Page, student_id: str):
    """
    Create the student details view.

    Args:
        page: The Flet page object
        student_id: The student ID

    Returns:
        ft.View: The student details view
    """
    # Get student details
    student = get_student_details(student_id)
    if not student:
        # Handle case where student is not found
        return ft.View(
            route=f"/student/{student_id}",
            controls=[
                ft.Container(
                    content=ft.Text("Étudiant non trouvé", size=20, color=ft.Colors.RED),
                    alignment=ft.alignment.center,
                    expand=True
                )
            ],
            appbar=create_app_bar(page, "Détails Étudiant")
        )

    # Check if mobile
    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'fr')

    # Create main content
    main_content = create_student_details_content(page, student)

    # Create the view layout
    if is_mobile:
        content = ft.Container(
            content=main_content,
            expand=True,
            padding=ft.padding.all(16)
        )

        view = ft.View(
            route=f"/student/{student_id}",
            controls=[content],
            appbar=create_app_bar(page, get_text("student_details", current_language)),
            drawer=create_navigation_drawer(page),
            padding=0
        )
    else:
        content = ft.Row([
            create_navigation_rail(page),
            ft.VerticalDivider(width=1),
            ft.Container(
                content=main_content,
                expand=True,
                padding=ft.padding.all(24)
            )
        ], expand=True)

        view = ft.View(
            route=f"/student/{student_id}",
            controls=[content],
            appbar=create_app_bar(page, get_text("student_details", current_language)),
            padding=0
        )

    return view


def create_student_details_content(page: ft.Page, student):
    """
    Create the main content for student details with improved design.

    Args:
        page: The Flet page object
        student: The student data

    Returns:
        ft.Column: The main content column
    """
    current_language = getattr(page, 'language', 'fr')

    # Back button with better styling
    back_button = ft.Container(
        content=ft.ElevatedButton(
            text=get_text("back_to_classes", current_language),
            icon=ft.Icons.ARROW_BACK,
            on_click=lambda _: page.go(ROUTE_CLASSES),
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_600,
                color=ft.Colors.WHITE,
                padding=ft.padding.symmetric(horizontal=20, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        ),
        margin=ft.margin.only(bottom=24)
    )

    # Modern header with student info and face image
    header_section = create_modern_student_header(student, current_language, getattr(page, 'is_mobile', False))

    # Student images section
    images_section = create_student_images_section(student, current_language, getattr(page, 'is_mobile', False))

    # Quiz history section
    quiz_section = create_quiz_history_section(page, student, current_language, getattr(page, 'is_mobile', False))

    return ft.Column([
        back_button,
        header_section,
        ft.Container(height=24),
        images_section,
        ft.Container(height=24),
        quiz_section,
    ], scroll=ft.ScrollMode.AUTO, expand=True)


def create_modern_student_header(student, current_language, is_mobile):
    """Create a modern, clean header with student information and face image."""

    # Format enrollment date
    enrollment_date = "N/A"
    if student.get('created_at'):
        try:
            date_obj = datetime.fromisoformat(student['created_at'].replace('Z', '+00:00'))
            enrollment_date = date_obj.strftime('%d/%m/%Y')
        except:
            enrollment_date = student['created_at']

    # Face encoding status
    face_status = get_text("face_enrolled", current_language) if student.get('has_face_encoding') else get_text("face_not_enrolled", current_language)
    face_color = ft.Colors.GREEN if student.get('has_face_encoding') else ft.Colors.RED

    # Profile image or icon avatar
    if student.get('profile_image_path') and os.path.exists(student['profile_image_path']):
        # Convert absolute path to relative path for Flet
        image_src = student['profile_image_path']
        if image_src.startswith('student_uploads/'):
            image_src = image_src[len('student_uploads/'):]
        elif 'student_uploads/' in image_src:
            image_src = image_src.split('student_uploads/')[-1]

        avatar = ft.Container(
            content=ft.Image(
                src=image_src,
                width=120,
                height=120,
                fit=ft.ImageFit.COVER,
                border_radius=60
            ),
            width=120,
            height=120,
            border_radius=60,
            border=ft.border.all(3, ft.Colors.BLUE_700)
        )
    else:
        avatar = ft.Container(
            content=ft.Icon(
                ft.Icons.PERSON,
                size=60,
                color=ft.Colors.WHITE
            ),
            width=120,
            height=120,
            bgcolor=ft.Colors.BLUE_600,
            border_radius=60,
            alignment=ft.alignment.center,
            border=ft.border.all(3, ft.Colors.BLUE_700)
        )

    # Student info section with better typography
    info_section = ft.Column([
        ft.Text(
            student['name'],
            size=32 if not is_mobile else 28,
            weight=ft.FontWeight.BOLD,
            color=ft.Colors.ON_SURFACE
        ),
        ft.Container(height=8),
        ft.Row([
            ft.Icon(ft.Icons.BADGE, size=18, color=ft.Colors.BLUE_600),
            ft.Text(f"ID: {student['id']}", size=16, color=ft.Colors.GREY_700, weight=ft.FontWeight.W_500)
        ], spacing=8),
        ft.Row([
            ft.Icon(ft.Icons.CLASS_, size=18, color=ft.Colors.BLUE_600),
            ft.Text(f"{get_text('class', current_language)}: {student.get('class_name', 'N/A')}", size=16, color=ft.Colors.GREY_700, weight=ft.FontWeight.W_500)
        ], spacing=8),
        ft.Row([
            ft.Icon(ft.Icons.CALENDAR_TODAY, size=18, color=ft.Colors.BLUE_600),
            ft.Text(f"{get_text('enrolled', current_language)}: {enrollment_date}", size=16, color=ft.Colors.GREY_700, weight=ft.FontWeight.W_500)
        ], spacing=8),
        ft.Container(height=12),
        ft.Container(
            content=ft.Text(
                face_status,
                size=14,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE
            ),
            bgcolor=face_color,
            padding=ft.padding.symmetric(horizontal=16, vertical=8),
            border_radius=20
        )
    ], spacing=8, expand=True)

    # Layout for mobile vs desktop
    if is_mobile:
        content = ft.Column([
            ft.Container(
                content=avatar,
                alignment=ft.alignment.center,
                margin=ft.margin.only(bottom=20)
            ),
            info_section
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
    else:
        content = ft.Row([
            avatar,
            ft.Container(width=32),  # Spacer
            info_section
        ], alignment=ft.MainAxisAlignment.START, vertical_alignment=ft.CrossAxisAlignment.CENTER)

    return ft.Card(
        content=ft.Container(
            content=content,
            padding=ft.padding.all(32),
            gradient=ft.LinearGradient(
                begin=ft.alignment.top_left,
                end=ft.alignment.bottom_right,
                colors=[ft.Colors.BLUE_50, ft.Colors.WHITE, ft.Colors.BLUE_50]
            )
        ),
        elevation=8,
        margin=ft.margin.symmetric(horizontal=16),
        surface_tint_color=ft.Colors.BLUE_50
    )


def create_student_images_section(student, current_language, is_mobile):
    """Create a section to display all student images."""
    images = student.get('images', [])

    if not images:
        return ft.Container()

    # Create image thumbnails
    thumbnails = []
    for image_path in images[:6]:  # Limit to 6 images
        print(f"DEBUG: Image path: {image_path}")
        
        if os.path.exists(image_path):
            # Convert absolute path to relative path for Flet
            image_src = image_path
            if image_src.startswith('student_uploads/'):
                image_src = image_src[len('student_uploads/'):]
            elif 'student_uploads/' in image_src:
                image_src = image_src.split('student_uploads/')[-1]

            thumbnails.append(
                ft.Container(
                    content=ft.Image(src=image_src, width=80, height=80, fit=ft.ImageFit.COVER),
                    width=80, height=80, border_radius=8,
                    border=ft.border.all(2, ft.Colors.BLUE_300), margin=4
                )
            )
    print(f"DEBUG: Thumbnails created: {thumbnails}")

    if not thumbnails:
        return ft.Container()

    return ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text("Images de l'étudiant", size=18, weight=ft.FontWeight.BOLD),
                ft.Container(height=12),
                ft.Row(thumbnails, scroll=ft.ScrollMode.AUTO, spacing=8)
            ]),
            padding=20
        ),
        elevation=4, margin=ft.margin.only(bottom=20)
    )


def create_quiz_history_section(page, student, current_language, is_mobile):
    """Create quiz history section with detailed quiz information."""

    def show_quiz_details(quiz_data):
        """Show detailed quiz results in a dialog with comprehensive information."""

        submission_id = quiz_data.get('submission_id')
        if not submission_id:
            # Fallback for basic info
            details_content = ft.Column([
                ft.Text(f"Quiz: {quiz_data['quiz_title']}", size=18, weight=ft.FontWeight.BOLD),
                ft.Text(f"Score: {quiz_data['score']}/{quiz_data['total_questions']} ({quiz_data['percentage']:.1f}%)", size=16),
                ft.Text(f"Date: {quiz_data['submitted_at'][:10]}", size=14, color=ft.Colors.GREY_600),
                ft.Divider(),
                ft.Text("Détails complets non disponibles pour ce quiz.",
                       size=14, color=ft.Colors.GREY_600, italic=True)
            ], spacing=8)
        else:
            # Get detailed submission data
            detailed_submission = get_quiz_submission_details(submission_id)
            if not detailed_submission:
                details_content = ft.Column([
                    ft.Text("Erreur lors du chargement des détails du quiz.",
                           size=14, color=ft.Colors.RED)
                ])
            else:
                details_content = create_detailed_quiz_content(detailed_submission)

        dialog = ft.AlertDialog(
            title=ft.Text("Détails du Quiz"),
            content=ft.Container(
                content=details_content,
                width=700,
                height=500
            ),
            actions=[
                ft.TextButton(get_text("close", current_language), on_click=lambda _: close_dialog(page))
            ]
        )

        show_dialog(page, dialog)

    # Get quiz results
    quiz_stats = student.get('quiz_stats', {})
    recent_quizzes = quiz_stats.get('recent_quizzes', [])

    quiz_items = []
    for quiz in recent_quizzes:
        percentage = quiz.get('percentage', 0)
        score_color = ft.Colors.GREEN if percentage >= 80 else ft.Colors.ORANGE if percentage >= 60 else ft.Colors.RED

        # Format date
        date_str = quiz.get('submitted_at', '')
        try:
            if 'T' in date_str:
                date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                formatted_date = date_obj.strftime('%d/%m/%Y %H:%M')
            else:
                formatted_date = date_str[:10]
        except:
            formatted_date = date_str[:10] if len(date_str) >= 10 else date_str

        quiz_items.append(
            ft.Container(
                content=ft.Row([
                    ft.Icon(ft.Icons.QUIZ, size=24, color=ft.Colors.PURPLE_600),
                    ft.Column([
                        ft.Text(quiz['quiz_title'], size=14, weight=ft.FontWeight.W_500),
                        ft.Text(formatted_date, size=12, color=ft.Colors.GREY_600),
                        ft.Text(f"{quiz['score']}/{quiz['total_questions']} questions", size=12, color=ft.Colors.GREY_600)
                    ], spacing=2, expand=True),
                    ft.Column([
                        ft.Container(
                            content=ft.Text(f"{percentage:.1f}%", size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE),
                            bgcolor=score_color,
                            padding=ft.padding.symmetric(horizontal=12, vertical=6),
                            border_radius=12
                        ),
                        ft.TextButton(
                            text="Détails",
                            on_click=lambda _, q=quiz: show_quiz_details(q),
                            style=ft.ButtonStyle(color=ft.Colors.BLUE_600)
                        )
                    ], horizontal_alignment=ft.CrossAxisAlignment.END, spacing=4)
                ], spacing=12),
                padding=ft.padding.all(16),
                margin=ft.margin.symmetric(vertical=4),
                bgcolor=ft.Colors.GREY_50,
                border_radius=8
            )
        )

    if not quiz_items:
        quiz_items.append(
            ft.Container(
                content=ft.Column([
                    ft.Icon(ft.Icons.QUIZ_OUTLINED, size=48, color=ft.Colors.GREY_400),
                    ft.Text("Aucun quiz complété", color=ft.Colors.GREY_600, size=16),
                    ft.Text("Les résultats des quiz apparaîtront ici", color=ft.Colors.GREY_500, size=12)
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER, spacing=16),
                padding=ft.padding.all(32),
                alignment=ft.alignment.center
            )
        )

    # Quiz statistics summary
    stats_summary = ft.Container(
        content=ft.Row([
            ft.Column([
                ft.Text("Total Quiz", size=12, color=ft.Colors.GREY_600),
                ft.Text(str(quiz_stats.get('total_quizzes', 0)), size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            ft.Container(width=1, height=40, bgcolor=ft.Colors.GREY_300),
            ft.Column([
                ft.Text("Moyenne", size=12, color=ft.Colors.GREY_600),
                ft.Text(f"{quiz_stats.get('average_score', 0):.1f}%", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600)
            ], horizontal_alignment=ft.CrossAxisAlignment.CENTER)
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.BLUE_50,
        border_radius=8,
        margin=ft.margin.only(bottom=16)
    )

    return ft.Card(
        content=ft.Container(
            content=ft.Column([
                ft.Text("Historique des Quiz", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.ON_SURFACE),
                ft.Container(height=8),
                stats_summary,
                ft.Column(quiz_items, scroll=ft.ScrollMode.AUTO, expand=True)
            ]),
            padding=ft.padding.all(24)
        ),
        elevation=4,
        margin=ft.margin.symmetric(horizontal=16)
    )


def create_detailed_quiz_content(detailed_submission):
    """Create detailed quiz content showing all questions and answers."""

    # Header information
    header = ft.Column([
        ft.Text(f"Quiz: {detailed_submission['quiz_title']}", size=20, weight=ft.FontWeight.BOLD),
        ft.Text(f"Score: {detailed_submission['score']}/{detailed_submission['total_questions']} ({detailed_submission['percentage']:.1f}%)",
               size=16, color=ft.Colors.BLUE_600),
        ft.Text(f"Date: {detailed_submission['submitted_at'][:16].replace('T', ' ')}",
               size=14, color=ft.Colors.GREY_600),
        ft.Divider()
    ], spacing=8)

    # Questions and answers
    questions_content = []
    detailed_answers = detailed_submission.get('detailed_answers', [])

    for i, answer in enumerate(detailed_answers, 1):
        # Question header
        question_header = ft.Container(
            content=ft.Row([
                ft.Text(f"Question {i}", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.CHECK_CIRCLE if answer['is_correct'] else ft.Icons.CANCEL,
                        color=ft.Colors.GREEN if answer['is_correct'] else ft.Colors.RED,
                        size=20
                    )
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            margin=ft.margin.only(bottom=8)
        )

        # Question text
        question_text = ft.Text(
            answer['question_text'],
            size=14,
            weight=ft.FontWeight.W_500,
            color=ft.Colors.ON_SURFACE
        )

        # Options
        options_content = []
        for option in answer['all_options']:
            is_selected = option['option_text'] in answer['selected_options']
            is_correct = option['is_correct']

            # Determine option style
            if is_selected and is_correct:
                # Selected and correct - green
                bg_color = ft.Colors.GREEN_100
                text_color = ft.Colors.GREEN_800
                icon = ft.Icons.CHECK_CIRCLE
                icon_color = ft.Colors.GREEN
            elif is_selected and not is_correct:
                # Selected but wrong - red
                bg_color = ft.Colors.RED_100
                text_color = ft.Colors.RED_800
                icon = ft.Icons.CANCEL
                icon_color = ft.Colors.RED
            elif not is_selected and is_correct:
                # Not selected but correct - light green
                bg_color = ft.Colors.GREEN_50
                text_color = ft.Colors.GREEN_700
                icon = ft.Icons.CHECK_CIRCLE_OUTLINE
                icon_color = ft.Colors.GREEN
            else:
                # Not selected and not correct - default
                bg_color = ft.Colors.GREY_50
                text_color = ft.Colors.GREY_700
                icon = ft.Icons.RADIO_BUTTON_UNCHECKED
                icon_color = ft.Colors.GREY_500

            option_container = ft.Container(
                content=ft.Row([
                    ft.Icon(icon, color=icon_color, size=16),
                    ft.Text(option['option_text'], size=12, color=text_color, expand=True)
                ], spacing=8),
                padding=ft.padding.all(8),
                bgcolor=bg_color,
                border_radius=4,
                margin=ft.margin.symmetric(vertical=2)
            )
            options_content.append(option_container)

        question_container = ft.Container(
            content=ft.Column([
                question_header,
                question_text,
                ft.Container(height=8),
                ft.Column(options_content)
            ]),
            padding=ft.padding.all(12),
            margin=ft.margin.symmetric(vertical=8),
            bgcolor=ft.Colors.SURFACE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT)
        )
        questions_content.append(question_container)

    return ft.Column([
        header,
        ft.Column(questions_content, scroll=ft.ScrollMode.AUTO, expand=True)
    ], scroll=ft.ScrollMode.AUTO)
