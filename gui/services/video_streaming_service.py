import cv2
import threading
import time
import face_recognition
import numpy as np
from flask import Flask, Response, render_template_string, request, jsonify
from facial_recognition_system.local_database import get_connection, get_students_in_class
import json
import os 

from hardware.JetsonCamera import Camera
from hardware.Focuser import Focuser

# Check if CUDA is available for video streaming
def check_cuda_availability():
    # Check if CUDA is explicitly disabled via environment variables
    if os.environ.get('CUDA_VISIBLE_DEVICES') == '' or os.environ.get('DLIB_USE_CUDA') == '0':
        print("DEBUG: CUDA disabled via environment variables for video streaming service")
        return False

    try:
        import dlib
        print("version dlib",dlib.DLIB_VERSION)
        if  dlib.cuda.get_num_devices() > 0:
            print("DEBUG: CUDA is available for video streaming service")
            return True
    except:
        pass
    print("DEBUG: CUDA not available for video streaming service, using CPU mode")
    return False

CUDA_AVAILABLE = check_cuda_availability()

app = Flask(__name__)

# Global variables
camera = None
focuser = None
output_frame = None
lock = threading.Lock()
streaming_active = False
current_class_id = None
current_class_name = None
current_subject_id = None
current_subject_name = None
recognized_students = set()
known_face_encodings = []
known_face_names = []
manual_attendance = {}  # Dictionary to store manual attendance overrides
all_class_students = []  # List of all students in the current class

# Fixed I2C bus to 8 and focus value
I2C_BUS = 7
FOCUS_VALUE = 200

def initialize_camera():
    global camera, focuser
    try:
        print("🔍 Initialisation de la caméra Jetson...")

        # Initialize camera like in FocuserExample.py
        camera = Camera()
        print("✅ Objet caméra créé")

        # Test camera by getting a frame
        test_frame = camera.getFrame(timeout=3000)  # 3 second timeout
        if test_frame is None:
            raise RuntimeError("Caméra initialisée mais ne peut pas capturer d'images - vérifiez la connexion")

        print(f"✅ Test caméra réussi - Taille de l'image: {test_frame.shape}")

        # Initialize focuser with fixed I2C bus 8
        try:
            print(f"🔍 Initialisation du focuser sur bus I2C {I2C_BUS}...")
            focuser = Focuser(I2C_BUS)
            print("✅ Focuser initialisé")

            # Set fixed focus value
            print(f"🔍 Réglage de la mise au point à {FOCUS_VALUE}...")
            focuser.set(Focuser.OPT_FOCUS, FOCUS_VALUE)
            print(f"✅ Mise au point réglée à {FOCUS_VALUE} sur bus I2C {I2C_BUS}")
        except Exception as focuser_error:
            print(f"⚠️  Échec de l'initialisation du focuser: {focuser_error}")
            print("⚠️  Continuation sans focuser (la caméra fonctionnera quand même)")
            focuser = None

        print("✅ Caméra Jetson initialisée")
        return True
    except Exception as e:
        error_msg = f"Échec de l'initialisation de la caméra: {type(e).__name__}: {str(e)}"
        print(f"❌ {error_msg}")

        # Provide specific troubleshooting based on error type
        if "RuntimeError" in str(type(e)):
            print("💡 Dépannage: Vérifiez la connexion du câble caméra CSI")
        elif "Permission" in str(e):
            print("💡 Dépannage: Exécutez avec sudo ou ajoutez l'utilisateur au groupe vidéo")
        elif "nvarguscamerasrc" in str(e):
            print("💡 Dépannage: Caméra CSI non détectée, essayez: sudo systemctl restart nvargus-daemon")
        elif "I2C" in str(e):
            print("💡 Dépannage: Problème de bus I2C, vérifiez la connexion du focuser")

        print("\n🔧 Commandes de diagnostic rapide:")
        print("• Vérifier caméra: ls -la /dev/video*")
        print("• Tester caméra: python3 test_camera.py")
        print("• Redémarrer daemon: sudo systemctl restart nvargus-daemon")
        print("• Vérifier logs: dmesg | grep -i camera")

        return False

def release_camera():
    global camera, focuser, streaming_active, output_frame
    streaming_active = False
    with lock:
        output_frame = None
    if camera is not None:
        try:
            camera.close()
        except Exception:
            pass
        finally:
            camera = None
            focuser = None

def load_student_data(class_id):
    global known_face_encodings, known_face_names, all_class_students, manual_attendance
    known_face_encodings = []
    known_face_names = []

    try:
        students = get_students_in_class(class_id)
        if not students:
            return False

        all_class_students = [{'name': name, 'id': student_id} for name, student_id in students]
        manual_attendance = {name: "Absent" for name, _ in students}

        student_ids = [student_id for _, student_id in students]
        conn = get_connection()
        cursor = conn.cursor()

        placeholders = ', '.join(['?' for _ in student_ids])
        query = f"SELECT name, code FROM etudiants WHERE id IN ({placeholders})"
        cursor.execute(query, student_ids)
        rows = cursor.fetchall()

        if not rows:
            return False

        for row in rows:
            name = row['name']
            code = json.loads(row['code'])

            if isinstance(code, list):
                if code and isinstance(code[0], list):
                    for encoding in code:
                        if len(encoding) == 128:
                            known_face_encodings.append(np.array(encoding))
                            known_face_names.append(name)
                elif len(code) == 128:
                    known_face_encodings.append(np.array(code))
                    known_face_names.append(name)

        return True
    except Exception:
        return False

def generate_frames():
    """Generate frames from the camera with face recognition"""
    global output_frame, lock, streaming_active, recognized_students

    # Initialize face recognition variables
    face_locations = []
    face_encodings = []
    face_names = []
    process_this_frame = True

    while streaming_active:
        if camera is None:
            print("❌ Camera is None in generate_frames")
            break

        try:
            frame = camera.getFrame(timeout=1000)  # 1 second timeout
            if frame is None:
                print("❌ Camera returned None frame")
                break
        except Exception as e:
            print(f"❌ Error getting frame: {type(e).__name__}: {str(e)}")
            break

        # Flip the frame horizontally to create a mirror effect
        frame = cv2.flip(frame, 1)

        # Only process every other frame to save time
        if process_this_frame and known_face_encodings:
            # Resize frame for faster face recognition processing
            small_frame = cv2.resize(frame, (0, 0), fx=0.25, fy=0.25)

            # Convert the image from BGR color (OpenCV) to RGB color (face_recognition)
            rgb_small_frame = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)

            # Find all the faces and face encodings in the current frame
            # Use CNN model if CUDA is available for better accuracy, otherwise use HOG for speed
            model = 'cnn' if CUDA_AVAILABLE else 'hog'

            try:
                face_locations = face_recognition.face_locations(rgb_small_frame, model=model)
                face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)
            except Exception as detection_error:
                if "cuda" in str(detection_error).lower() or "gpu" in str(detection_error).lower():
                    print(f"DEBUG: GPU error in video streaming, falling back to HOG: {detection_error}")
                    face_locations = face_recognition.face_locations(rgb_small_frame, model='hog')
                    face_encodings = face_recognition.face_encodings(rgb_small_frame, face_locations)
                else:
                    raise detection_error

            face_names = []
            for face_encoding in face_encodings:
                try:
                    # Only proceed if we have known face encodings to compare against
                    if known_face_encodings and len(known_face_encodings) > 0:
                        # See if the face is a match for the known face(s)
                        matches = face_recognition.compare_faces(known_face_encodings, face_encoding, tolerance=0.6)
                        name = "Unknown"

                        # Use the known face with the smallest distance to the new face
                        face_distances = face_recognition.face_distance(known_face_encodings, face_encoding)
                        best_match_index = np.argmin(face_distances)
                        if matches[best_match_index]:
                            name = known_face_names[best_match_index]
                            recognized_students.add(name)
                    else:
                        name = "Unknown"
                except Exception:
                    name = "Unknown"

                face_names.append(name)

        process_this_frame = not process_this_frame

        # Display the results
        for (top, right, bottom, left), name in zip(face_locations, face_names):
            # Scale back up face locations since the frame we detected in was scaled to 1/4 size
            top *= 4
            right *= 4
            bottom *= 4
            left *= 4

            color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
            cv2.rectangle(frame, (left, bottom - 35), (right, bottom), color, cv2.FILLED)
            cv2.putText(frame, name, (left + 6, bottom - 6), cv2.FONT_HERSHEY_DUPLEX, 0.6, (255, 255, 255), 1)

        if current_class_name:
            cv2.rectangle(frame, (10, 10), (400, 80), (40, 44, 52), cv2.FILLED)
            cv2.putText(frame, f"Classe: {current_class_name}", (20, 35), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)
            cv2.putText(frame, f"Étudiants: {len(recognized_students)}/{len(set(known_face_names))}", (20, 65), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 1)

        # Acquire the lock, set the output frame, and release the lock
        with lock:
            output_frame = frame.copy()



def generate():
    """Generate the response containing the video frames"""
    global output_frame, lock

    while streaming_active:
        # Wait until the lock is acquired
        with lock:
            # Check if the output frame is available
            if output_frame is None:
                continue

            # Encode the frame in JPEG format
            (flag, encoded_image) = cv2.imencode(".jpg", output_frame)

            # Ensure the frame was successfully encoded
            if not flag:
                continue

        # Yield the output frame in the byte format
        yield(b'--frame\r\n' b'Content-Type: image/jpeg\r\n\r\n' +
              bytearray(encoded_image) + b'\r\n')

        # Sleep for a short duration
        time.sleep(0.03)

@app.route('/video_feed')
def video_feed():
    """Route for video feed"""
    return Response(generate(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/start_stream', methods=['POST'])
def start_stream():
    """Start the video stream for a specific class"""
    global streaming_active, current_class_id, current_class_name, current_subject_id, current_subject_name, recognized_students, manual_attendance, all_class_students

    # Get class and subject information from request
    data = request.json
    class_id = data.get('class_id')
    class_name = data.get('class_name')
    subject_id = data.get('subject_id')
    subject_name = data.get('subject_name')

    if not class_id or not class_name:
        return jsonify({'success': False, 'message': 'Informations de classe requises'}), 400

    # Initialize camera if not already initialized
    if camera is None and not initialize_camera():
        error_msg = 'Échec de l\'initialisation de la caméra. Vérifiez: 1) Connexion caméra CSI, 2) Exécutez: sudo systemctl restart nvargus-daemon, 3) Permissions: sudo usermod -a -G video $USER'
        return jsonify({'success': False, 'message': error_msg}), 500

    # Load student data for the class
    if not load_student_data(class_id):
        return jsonify({'success': False, 'message': 'Échec du chargement des données étudiantes'}), 500

    # Set current class and subject information
    current_class_id = class_id
    current_class_name = class_name
    current_subject_id = subject_id
    current_subject_name = subject_name
    recognized_students = set()
    manual_attendance = {}
    all_class_students = []

    # Start streaming if not already active
    if not streaming_active:
        streaming_active = True
        threading.Thread(target=generate_frames, daemon=True).start()

    return jsonify({'success': True, 'message': 'Flux démarré avec succès'})

@app.route('/stop_stream', methods=['POST'])
def stop_stream():
    """Stop the video stream"""
    global streaming_active

    streaming_active = False
    release_camera()

    return jsonify({'success': True, 'message': 'Flux arrêté avec succès'})

@app.route('/get_recognized_students', methods=['GET'])
def get_recognized_students():
    """Get the list of recognized students"""
    global recognized_students

    return jsonify({
        'success': True,
        'recognized_students': list(recognized_students),
        'total_students': len(set(known_face_names)) if known_face_names else 0
    })

@app.route('/get_class_students', methods=['GET'])
def get_class_students():
    """Get all students in the current class with their attendance status"""
    global all_class_students, manual_attendance, recognized_students

    students_with_status = []
    for student in all_class_students:
        name = student['name']
        # Check if student is recognized by facial recognition or manually marked present
        is_present = name in recognized_students or manual_attendance.get(name) == "Present"
        students_with_status.append({
            'name': name,
            'id': student['id'],
            'status': 'Present' if is_present else 'Absent',
            'recognized': name in recognized_students,
            'manual': manual_attendance.get(name, 'Absent')
        })

    return jsonify({
        'success': True,
        'students': students_with_status
    })

@app.route('/toggle_attendance', methods=['POST'])
def toggle_attendance():
    """Toggle manual attendance for a student"""
    global manual_attendance

    data = request.json
    student_name = data.get('student_name')
    new_status = data.get('status')

    if not student_name or new_status not in ['Present', 'Absent']:
        return jsonify({'success': False, 'message': 'Données invalides'}), 400

    manual_attendance[student_name] = new_status

    return jsonify({
        'success': True,
        'message': f'Présence mise à jour pour {student_name}',
        'student_name': student_name,
        'status': new_status
    })

@app.route('/take_attendance', methods=['POST'])
def take_attendance():
    """Capture current recognition state and update attendance"""
    global recognized_students, manual_attendance, all_class_students, streaming_active

    # Update manual attendance based on current recognition state
    for student in all_class_students:
        name = student['name']
        if name in recognized_students:
            manual_attendance[name] = "Present"

    streaming_active = False
    release_camera()

    return jsonify({
        'success': True,
        'message': 'Présence capturée depuis le flux vidéo',
        'recognized_count': len(recognized_students),
        'total_students': len(all_class_students)
    })

@app.route('/save_attendance', methods=['POST'])
def save_attendance():
    """Save attendance data to database"""
    global manual_attendance, current_class_id, current_subject_id

    try:
        from gui.services.attendance_service import save_attendance

        if not manual_attendance:
            return jsonify({
                'success': True,
                'message': 'Aucun étudiant dans la classe - rien à sauvegarder'
            })

        class_id = int(current_class_id) if current_class_id and str(current_class_id).isdigit() else None
        subject_id = int(current_subject_id) if current_subject_id and str(current_subject_id).isdigit() else None

        success = save_attendance(
            attendance_data=manual_attendance,
            class_id=class_id,
            subject_id=subject_id
        )

        if success:
            return jsonify({
                'success': True,
                'message': 'Présence sauvegardée avec succès'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Échec de la sauvegarde de la présence'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erreur lors de la sauvegarde de la présence: {str(e)}'
        }), 500

@app.route('/stream_page')
def stream_page():
    """Render the video streaming page"""
    class_id = request.args.get('class_id', '')
    class_name = request.args.get('class_name', '')
    subject_id = request.args.get('subject_id', '')
    subject_name = request.args.get('subject_name', '')
    # Start the stream immediately
    global streaming_active, current_class_id, current_class_name, current_subject_id, current_subject_name, recognized_students, manual_attendance, all_class_students

    # Set current class and subject information
    current_class_id = class_id
    current_class_name = class_name
    current_subject_id = subject_id
    current_subject_name = subject_name
    recognized_students = set()
    manual_attendance = {}
    all_class_students = []

    try:
        if camera is None:
            if not initialize_camera():
                error_details = "Échec de l'initialisation de la caméra. Causes possibles:\n"
                error_details += "• Caméra CSI mal connectée\n"
                error_details += "• Câble caméra desserré ou endommagé\n"
                error_details += "• Problèmes de permissions (essayez d'exécuter avec sudo)\n"
                error_details += "• nvargus-daemon non démarré (essayez: sudo systemctl restart nvargus-daemon)"
                return f"Erreur: Échec de l'initialisation de la caméra.\n\n{error_details}", 500

        if not load_student_data(class_id):
            pass

        if not streaming_active:
            streaming_active = True
            threading.Thread(target=generate_frames, daemon=True).start()
    except Exception as e:
        error_details = f"Erreur de démarrage du flux: {type(e).__name__}: {str(e)}\n\n"
        error_details += "Étapes de dépannage:\n"
        error_details += "1. Vérifiez la connexion de la caméra\n"
        error_details += "2. Redémarrez nvargus-daemon: sudo systemctl restart nvargus-daemon\n"
        error_details += "3. Vérifiez les permissions: sudo usermod -a -G video $USER\n"
        error_details += "4. Vérifiez la caméra avec: ls -la /dev/video*"
        return f"Erreur lors du démarrage du flux:\n\n{error_details}", 500

    # HTML template for the streaming page
    html  = '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Flux Vidéo de Présence</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 15px; background: #f0f0f0; }
            .container { max-width: 1200px; margin: 0 auto; }
            .video-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .attendance-section { background: white; padding: 20px; border-radius: 8px; }
            .video-container { margin: 15px 0; text-align: center; }
            .video-feed { max-width: 100%; height: auto; border: 2px solid #ddd; border-radius: 8px; }
            h2, h3 { color: #333; margin: 0 0 15px 0; }
            .info { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 8px 0; display: flex; justify-content: space-between; }
            .button { background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
            .button.primary { background: #007bff; }
            .button.success { background: #28a745; }
            .student-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #ddd; }
            .student-item.present { background: #d4edda; border-color: #28a745; }
            .student-item.absent { background: #f8d7da; border-color: #dc3545; }
            .student-status { display: flex; align-items: center; gap: 8px; }
            .status-badge { padding: 3px 8px; border-radius: 3px; font-size: 12px; color: white; }
            .status-badge.present { background: #28a745; }
            .status-badge.absent { background: #dc3545; }
            .status-badge.recognized { background: #007bff; }
            .toggle-btn { padding: 5px 10px; font-size: 12px; border-radius: 3px; border: none; cursor: pointer; color: white; }
            .toggle-btn.present { background: #dc3545; }
            .toggle-btn.absent { background: #28a745; }
            .action-buttons { text-align: center; margin: 20px 0; }
            .loading { opacity: 0.6; pointer-events: none; }
        </style>
    </head>
    <body>
        <div class="container">
            <!-- Video Section -->
            <div class="video-section">
                <h2>Flux Vidéo en Direct</h2>
                <div class="info">
                    <span>Classe:</span>
                    <span id="className">{{class_name}}</span>
                </div>
                <div class="info">
                    <span>Matière:</span>
                    <span id="subjectName">{{subject_name}}</span>
                </div>
                <div class="info">
                    <span>Étudiants reconnus:</span>
                    <span id="studentCount">0/0</span>
                </div>

                <div class="video-container">
                    <img src="{{ url_for('video_feed') }}" class="video-feed">
                </div>

                <div class="action-buttons">
                    <button id="takeAttendanceBtn" class="button primary" onclick="takeAttendance()">Prendre Présence</button>
                    <button id="saveAttendanceBtn" class="button success" onclick="saveAttendance()" style="display: none;">Sauvegarder</button>
                    <button id="stopBtn" class="button" onclick="stopStream()">Arrêter</button>
                </div>
            </div>

            <!-- Attendance Section -->
            <div class="attendance-section">
                <h3>Liste de Présence</h3>
                <div id="studentList" class="student-list">
                    <!-- Students will be loaded here -->
                </div>
            </div>
        </div>

        <script>
            let students = [];

            // Update student count and list periodically
            function updateStudentData() {
                // Update recognized count
                fetch('/get_recognized_students')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            document.getElementById('studentCount').textContent =
                                `${data.recognized_students.length}/${data.total_students}`;
                        }
                    })
                    .catch(error => console.error('Error:', error));

                // Update student list
                fetch('/get_class_students')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            students = data.students;
                            renderStudentList();
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }

            function renderStudentList() {
                const studentList = document.getElementById('studentList');
                if (students.length === 0) {
                    studentList.innerHTML = '<p style="text-align: center; color: #666;">Aucun étudiant inscrit</p>';
                    return;
                }

                studentList.innerHTML = students.map(student => {
                    const statusClass = student.status === 'Present' ? 'present' : 'absent';
                    const toggleText = student.status === 'Present' ? 'Marquer Absent' : 'Marquer Présent';
                    const toggleClass = student.status === 'Present' ? 'present' : 'absent';

                    const statusText = student.status === 'Present' ? 'Présent' : 'Absent';
                    let badges = `<span class="status-badge ${student.status.toLowerCase()}">${statusText}</span>`;
                    if (student.recognized) badges += `<span class="status-badge recognized">Reconnu</span>`;

                    return `
                        <div class="student-item ${statusClass}">
                            <div class="student-name">${student.name}</div>
                            <div class="student-status">
                                ${badges}
                                <button class="toggle-btn ${toggleClass}" onclick="toggleStudentAttendance('${student.name}', '${student.status === 'Present' ? 'Absent' : 'Present'}')">${toggleText}</button>
                            </div>
                        </div>`;
                }).join('');
            }

            // Toggle individual student attendance
            function toggleStudentAttendance(studentName, newStatus) {
                fetch('/toggle_attendance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        student_name: studentName,
                        status: newStatus
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update local data
                        const student = students.find(s => s.name === studentName);
                        if (student) {
                            student.status = newStatus;
                            student.manual = newStatus;
                        }
                        renderStudentList();
                    } else {
                        alert('Erreur lors de la mise à jour de la présence: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors de la mise à jour de la présence');
                });
            }

            function takeAttendance() {
                const btn = document.getElementById('takeAttendanceBtn');
                const saveBtn = document.getElementById('saveAttendanceBtn');
                const videoFeed = document.querySelector('.video-feed');

                btn.classList.add('loading');
                btn.textContent = 'Capture en cours...';

                fetch('/take_attendance', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStudentData();

                        if (videoFeed) {
                            videoFeed.style.display = 'none';
                            const videoContainer = document.querySelector('.video-container');
                            if (videoContainer) {
                                const message = document.createElement('div');
                                message.style.cssText = 'padding: 40px 20px; text-align: center; background: #f8f9fa; border-radius: 8px; border: 2px dashed #ddd; color: #666; font-size: 16px;';
                                message.innerHTML = '<div style="font-size: 36px; margin-bottom: 10px;">📸</div><div>Présence capturée avec succès</div><div style="font-size: 14px; margin-top: 8px; color: #999;">Enregistrement vidéo arrêté</div>';
                                videoContainer.appendChild(message);
                            }
                        }

                        btn.style.display = 'none';
                        saveBtn.style.display = 'inline-block';
                        stopUpdates();
                        alert(`Présence capturée: ${data.recognized_count} étudiants reconnus`);
                    } else {
                        alert('Erreur lors de la capture de présence: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors de la capture de présence');
                })
                .finally(() => {
                    btn.classList.remove('loading');
                    btn.textContent = 'Prendre Présence';
                });
            }

            function saveAttendance() {
                const btn = document.getElementById('saveAttendanceBtn');
                btn.classList.add('loading');
                btn.textContent = 'Sauvegarde...';

                fetch('/save_attendance', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Présence sauvegardée avec succès');
                        setTimeout(() => window.close(), 1000);
                    } else {
                        alert('Erreur lors de la sauvegarde: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Erreur lors de la sauvegarde');
                })
                .finally(() => {
                    btn.classList.remove('loading');
                    btn.textContent = 'Sauvegarder';
                });
            }

            function stopStream() {
                fetch('/stop_stream', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                    window.close();
                })
                .catch(error => console.error('Error:', error));
            }

            let updateInterval;
            function init() {
                updateStudentData();
                updateInterval = setInterval(updateStudentData, 3000);
            }

            function stopUpdates() {
                if (updateInterval) {
                    clearInterval(updateInterval);
                    updateInterval = null;
                }
            }

            function handlePageClose() {
                fetch('/stop_stream', { method: 'POST', keepalive: true }).catch(error => console.log('Error stopping stream on page close:', error));
            }

            window.addEventListener('beforeunload', handlePageClose);
            window.addEventListener('unload', handlePageClose);
            window.addEventListener('pagehide', handlePageClose);
            document.addEventListener('DOMContentLoaded', init);
        </script>
    </body>
    </html>
    '''

    return render_template_string(
        html,
        class_id=class_id,
        class_name=class_name,
        subject_id=subject_id,
        subject_name=subject_name
    )

def run_server(host=None, port=None):
    """
    Run the video streaming server.

    Args:
        host: The host to bind the server to (default from Config)
        port: The port to bind the server to (default from Config)
    """
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.VIDEO_STREAM_PORT
    try:
        app.run(host=host, port=port, threaded=True)
    except Exception:
        raise

if __name__ == '__main__':
    run_server()
