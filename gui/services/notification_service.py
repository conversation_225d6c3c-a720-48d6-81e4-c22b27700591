"""
Notification service for real-time updates across the application.
"""
import threading
import time
from typing import Dict, List, Callable

# Global registry for notification callbacks
_notification_callbacks: Dict[str, List[Callable]] = {
    'student_enrolled': [],
    'admin_data_refresh': []
}

# Thread-safe lock for callback registry
_callback_lock = threading.Lock()

def register_callback(event_type: str, callback: Callable):
    """Register a callback for a specific event type."""
    with _callback_lock:
        if event_type not in _notification_callbacks:
            _notification_callbacks[event_type] = []
        _notification_callbacks[event_type].append(callback)
        print(f"DEBUG: Registered callback for {event_type}")

def unregister_callback(event_type: str, callback: Callable):
    """Unregister a callback for a specific event type."""
    with _callback_lock:
        if event_type in _notification_callbacks:
            try:
                _notification_callbacks[event_type].remove(callback)
                print(f"DEBUG: Unregistered callback for {event_type}")
            except ValueError:
                pass

def notify_event(event_type: str, *args, **kwargs):
    """Notify all registered callbacks for an event type."""
    with _callback_lock:
        callbacks = _notification_callbacks.get(event_type, []).copy()
    
    if callbacks:
        print(f"DEBUG: Notifying {len(callbacks)} callbacks for {event_type}")
        for callback in callbacks:
            try:
                # Run callback in a separate thread to avoid blocking
                threading.Thread(
                    target=callback,
                    args=args,
                    kwargs=kwargs,
                    daemon=True
                ).start()
            except Exception as e:
                print(f"DEBUG: Error in notification callback: {e}")

def notify_student_enrolled(student_name: str):
    """Notify that a new student has been enrolled."""
    print(f"DEBUG: Notifying student enrollment: {student_name}")
    notify_event('student_enrolled', student_name=student_name)
    
    # Also trigger admin data refresh
    notify_event('admin_data_refresh')

def notify_admin_data_refresh():
    """Notify that admin data should be refreshed."""
    print("DEBUG: Notifying admin data refresh")
    notify_event('admin_data_refresh')

# Convenience functions for common notifications
def setup_admin_refresh_callback(page):
    """Setup automatic refresh callback for admin pages."""
    def refresh_admin_data():
        try:
            # Check if page has refresh function
            if hasattr(page, '_refresh_admin_data'):
                print("DEBUG: Triggering admin data refresh")
                page._refresh_admin_data()
            else:
                print("DEBUG: No admin refresh function found on page")
        except Exception as e:
            print(f"DEBUG: Error refreshing admin data: {e}")
    
    register_callback('admin_data_refresh', refresh_admin_data)
    return refresh_admin_data

def cleanup_callbacks():
    """Clear all notification callbacks."""
    with _callback_lock:
        for event_type in _notification_callbacks:
            _notification_callbacks[event_type].clear()
        print("DEBUG: Cleared all notification callbacks")
