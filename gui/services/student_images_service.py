import os
import shutil
from pathlib import Path
from facial_recognition_system.config import Config

def get_student_folder_path(student_name, class_id=None):
    """Get the folder path for a student's images"""
    # Use the actual upload folder from enrollment service
    upload_base = Path("student_uploads")

    # Search in class folder if class_id provided
    if class_id:
        class_folder = upload_base / f"class_{class_id}"
        if class_folder.exists():
            for folder in class_folder.iterdir():
                if folder.is_dir():
                    info_file = folder / "info.txt"
                    if info_file.exists():
                        try:
                            with open(info_file, 'r') as f:
                                content = f.read()
                                # Match full name or first name
                                if f"Name: {student_name.split()[0]}" in content:
                                    return folder
                        except:
                            continue

    # Search in root upload folder
    if upload_base.exists():
        for folder in upload_base.iterdir():
            if folder.is_dir() and not folder.name.startswith("class_"):
                info_file = folder / "info.txt"
                if info_file.exists():
                    try:
                        with open(info_file, 'r') as f:
                            content = f.read()
                            if f"Name: {student_name.split()[0]}" in content:
                                return folder
                    except:
                        continue
    return None

def get_student_images(student_name, class_id=None):
    """Get all images for a student"""
    try:
        student_folder = get_student_folder_path(student_name, class_id)
        if not student_folder or not student_folder.exists():
            return []

        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        images = []

        for file_path in student_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                images.append(str(file_path))

        return sorted(images)
    except Exception as e:
        print(f"Error getting student images: {e}")
        return []

def get_profile_image_path(student_name, class_id=None):
    """Get the profile image path for a student"""
    images = get_student_images(student_name, class_id)
    return images[0] if images else None

def create_profile_image(source_image_path, student_folder, student_name):
    """Create a profile image from the first uploaded image"""
    try:
        profile_image_name = f"profile.jpg"
        profile_image_path = os.path.join(student_folder, profile_image_name)
        
        # Copy the first image as profile image
        shutil.copy2(source_image_path, profile_image_path)
        
        return profile_image_path
    except Exception as e:
        print(f"Error creating profile image: {e}")
        return None
